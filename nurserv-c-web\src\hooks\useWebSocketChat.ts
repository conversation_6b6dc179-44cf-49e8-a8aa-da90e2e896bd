import { useCallback, useEffect } from 'react';
import useWebSocket, { WebSocketMessage } from './useWebSocket';
import { useWebSocketData } from './useWebSocketData';
import {
  GetConversationsParams,
  GetMessagesParams,
  CreateConversationRequest,
} from '@/store/api/chatApiSlice';

interface UseWebSocketChatOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onOpen?: (event: WebSocketEventMap['open']) => void;
  onClose?: (event: WebSocketEventMap['close']) => void;
  onError?: (event: WebSocketEventMap['error']) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

export const useWebSocketChat = (options: UseWebSocketChatOptions) => {
  // Initialize data management
  const dataManager = useWebSocketData({
    sendMessage: () => false, // Placeholder, will be replaced
    onMessage: options.onMessage,
  });

  // Combined message handler - memoized to prevent unnecessary reconnections
  const handleMessage = useCallback((message: WebSocketMessage) => {
    // Let the data manager handle the message first
    dataManager.handleWebSocketMessage(message);
    // Also call the original onMessage if provided
    if (options.onMessage) {
      options.onMessage(message);
    }
  }, [dataManager.handleWebSocketMessage, options.onMessage]);

  // Initialize single WebSocket connection with combined message handler
  const webSocket = useWebSocket({
    ...options,
    onMessage: handleMessage,
  });

  // Update the data manager's sendMessage function
  useEffect(() => {
    (dataManager as any).sendMessage = webSocket.sendMessage;
  }, [dataManager, webSocket.sendMessage]);

  // Create query-like functions that match the RTK Query API
  const useGetConversationsQuery = useCallback((
    params: GetConversationsParams = {},
    options: { skip?: boolean } = {}
  ) => {
    const { skip = false } = options;
    const isLoading = dataManager.loading.conversations || false;
    const error = dataManager.errors.conversations;

    const refetch = useCallback(() => {
      if (!skip) {
        return dataManager.getConversations(params);
      }
      return Promise.resolve();
    }, [skip, params]);

    // Auto-fetch on mount if not skipped
    if (!skip && !isLoading && !dataManager.conversations.length && !error) {
      dataManager.getConversations(params);
    }

    return {
      data: {
        success: !error,
        conversations: dataManager.conversations,
        message: error || undefined,
      },
      isLoading,
      error: error ? new Error(error) : null,
      refetch,
    };
  }, [dataManager]);

  const useGetConversationQuery = useCallback((
    conversationId: string,
    options: { skip?: boolean } = {}
  ) => {
    const { skip = false } = options;
    const isLoading = dataManager.loading[`conversation_${conversationId}`] || false;
    const error = dataManager.errors[`conversation_${conversationId}`];
    const conversation = dataManager.conversationDetails[conversationId];

    const refetch = useCallback(() => {
      if (!skip && conversationId) {
        return dataManager.getConversation(conversationId);
      }
      return Promise.resolve();
    }, [skip, conversationId]);

    // Auto-fetch on mount if not skipped
    if (!skip && conversationId && !isLoading && !conversation && !error) {
      dataManager.getConversation(conversationId);
    }

    return {
      data: conversation ? {
        success: true,
        conversation,
        data: { conversation },
      } : undefined,
      isLoading,
      error: error ? new Error(error) : null,
      refetch,
    };
  }, [dataManager]);

  const useGetMessagesQuery = useCallback((
    params: GetMessagesParams,
    options: { skip?: boolean; pollingInterval?: number } = {}
  ) => {
    const { skip = false } = options;
    const { conversationId } = params;
    const isLoading = dataManager.loading[`messages_${conversationId}`] || false;
    const error = dataManager.errors[`messages_${conversationId}`];
    const messages = dataManager.messages[conversationId] || [];

    const refetch = useCallback(() => {
      if (!skip && conversationId) {
        return dataManager.getMessages(params);
      }
      return Promise.resolve();
    }, [skip, params]);

    // Auto-fetch on mount if not skipped
    if (!skip && conversationId && !isLoading && !messages.length && !error) {
      dataManager.getMessages(params);
    }

    return {
      data: {
        success: !error,
        messages,
        data: { messages },
      },
      isLoading,
      error: error ? new Error(error) : null,
      refetch,
    };
  }, [dataManager]);

  // Create mutation-like functions
  const useCreateConversationMutation = useCallback(() => {
    const mutate = useCallback(async (data: CreateConversationRequest) => {
      const result = await dataManager.createConversation(data);
      return {
        unwrap: () => Promise.resolve(result)
      };
    }, [dataManager]);

    return [mutate, { isLoading: false }];
  }, [dataManager]);

  const useMarkMessagesAsReadMutation = useCallback(() => {
    const mutate = useCallback(async (conversationId: string) => {
      const result = await dataManager.markMessagesAsRead(conversationId);
      return {
        unwrap: () => Promise.resolve(result)
      };
    }, [dataManager]);

    return [mutate, { isLoading: false }];
  }, [dataManager]);

  const useUpdateConversationStatusMutation = useCallback(() => {
    const mutate = useCallback(async (data: { conversationId: string; status: 'active' | 'inactive' | 'archived' }) => {
      const result = await dataManager.updateConversationStatus(data.conversationId, data.status);
      return {
        unwrap: () => Promise.resolve(result)
      };
    }, [dataManager]);

    return [mutate, { isLoading: false }];
  }, [dataManager]);

  return {
    // WebSocket connection methods
    ...webSocket,
    
    // Data state
    conversations: dataManager.conversations,
    conversationDetails: dataManager.conversationDetails,
    messages: dataManager.messages,
    loading: dataManager.loading,
    errors: dataManager.errors,
    
    // Query-like hooks (to replace RTK Query hooks)
    useGetConversationsQuery,
    useGetConversationQuery,
    useGetMessagesQuery,
    
    // Mutation-like hooks
    useCreateConversationMutation,
    useMarkMessagesAsReadMutation,
    useUpdateConversationStatusMutation,
    
    // Direct operations
    getConversations: dataManager.getConversations,
    getConversation: dataManager.getConversation,
    getMessages: dataManager.getMessages,
    createConversation: dataManager.createConversation,
    markMessagesAsRead: dataManager.markMessagesAsRead,
    updateConversationStatus: dataManager.updateConversationStatus,
  };
};

export default useWebSocketChat;
