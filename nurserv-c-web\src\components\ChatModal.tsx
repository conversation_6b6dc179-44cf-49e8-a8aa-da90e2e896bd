import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, MessageSquare, X, Send, AlertCircle } from 'lucide-react';
import { WebSocketMessage } from '@/hooks/useWebSocket';
import useWebSocketChat from '@/hooks/useWebSocketChat';
import {
  useSendMessageMutation,
  Message,
} from '@/store/api/chatApiSlice';
import { format, isValid } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

interface ChatModalProps {
  selectedNurse: {
    nurse_cognitoId: string;
    nurse_given_name: string;
  } | null;
  isOpen: boolean;
  onClose: () => void;
}

const ChatModal: React.FC<ChatModalProps> = ({
  selectedNurse,
  isOpen,
  onClose,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [conversationId, setConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousConversationIdRef = useRef<string | null>(null);
  const { toast } = useToast();

  const safeFormatDate = (
    timestamp: string | undefined,
    formatString: string,
    fallback: string = '--'
  ) => {
    if (!timestamp) return fallback;

    const date = new Date(timestamp);
    if (!isValid(date)) return fallback;

    try {
      return format(date, formatString);
    } catch (error) {
      console.error('Error formatting date:', error);
      return fallback;
    }
  };

  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const [sendMessage, { isLoading: isSendingMessage }] =
    useSendMessageMutation();

  const baseWsUrl =
    import.meta.env.VITE_CHAT_WS_URL || 'ws://localhost:8004/ws';
  const wsUrl = baseWsUrl.endsWith('/ws')
    ? baseWsUrl
    : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  const webSocketChat = useWebSocketChat({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled: isOpen,
    onMessage: handleWebSocketMessage,
  });

  const {
    status: wsStatus,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
    useGetConversationQuery,
    useGetMessagesQuery,
    useCreateConversationMutation,
    useMarkMessagesAsReadMutation,
  } = webSocketChat;

  const [createConversation, { isLoading: isCreatingConversation }] =
    useCreateConversationMutation();
  const [markAsRead] = useMarkMessagesAsReadMutation();

  // Create stable reference to markAsRead function
  const markAsReadRef = useRef(markAsRead);
  markAsReadRef.current = markAsRead;

  // WebSocket-based mark as read functionality
  const markAsReadImmediately = useCallback(() => {
    if (!conversationId) return;
    markAsReadRef.current(conversationId).catch(err =>
      console.error('Error marking messages as read:', err)
    );
  }, [conversationId]);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const debouncedMarkAsRead = useCallback(() => {
    if (!conversationId) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      markAsReadRef.current(conversationId).catch(err =>
        console.error('Error marking messages as read:', err)
      );
    }, 1000);
  }, [conversationId]);

  const observerRef = useRef<IntersectionObserver | null>(null);
  const setupMessageObserver = useCallback(
    (messageElements: NodeListOf<Element>) => {
      if (!conversationId || !userId) return;

      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      observerRef.current = new IntersectionObserver(
        entries => {
          let shouldMarkAsRead = false;

          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const messageElement = entry.target as HTMLElement;
              const senderId = messageElement.dataset.senderId;

              if (senderId && senderId !== userId) {
                shouldMarkAsRead = true;
              }
            }
          });

          if (shouldMarkAsRead) {
            debouncedMarkAsRead();
          }
        },
        {
          threshold: 0.5,
          rootMargin: '0px 0px -50px 0px',
        }
      );

      messageElements.forEach(element => {
        observerRef.current?.observe(element);
      });
    },
    [conversationId, userId, debouncedMarkAsRead]
  );

  const {
    data: _conversationData,
    isLoading: isLoadingConversation,
    error: _conversationError,
  } = useGetConversationQuery(conversationId || '', {
    skip: !conversationId,
  });

  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    error: _messagesError,
    refetch: refetchMessages,
  } = useGetMessagesQuery(
    { conversationId: conversationId || '', page: 1, limit: 50 },
    {
      skip: !conversationId,
      pollingInterval: 0,
    }
  );

  function handleWebSocketMessage(message: WebSocketMessage) {
    if (
      message.type === 'TEXT_MESSAGE' &&
      message.conversationId === conversationId
    ) {
      setMessages(prevMessages => {
        const messageExists = prevMessages.some(
          m =>
            m.content === message.content &&
            Math.abs(
              new Date(m.timestamp).getTime() -
                new Date(message.timestamp || Date.now()).getTime()
            ) < 5000
        );

        if (messageExists) {
          return prevMessages;
        }

        const newMsg: Message = {
          id: message.content || String(Date.now()),
          conversationId: message.conversationId || conversationId,
          senderId: message.senderId || '',
          senderType: message.senderType || 'nurse',
          senderName: message.senderName || 'Unknown',
          content: message.content || '',
          type: 'text',
          status: 'sent',
          timestamp: message.timestamp || new Date().toISOString(),
        };

        return [...prevMessages, newMsg];
      });

      if (message.senderId !== userId && conversationId) {
        sendReadReceipt(conversationId);
        debouncedMarkAsRead();
      }
    }
  }

  useEffect(() => {
    if (selectedNurse && isOpen && !conversationId) {
      const createConversationForNurse = async () => {
        // Check if WebSocket is connected
        if (!wsStatus.connected) {
          toast({
            title: 'Connection Error',
            description: 'Chat service is not available. Please try again later.',
            variant: 'destructive',
          });
          onClose();
          return;
        }

        try {
          const response = await createConversation({
            nurseId: selectedNurse.nurse_cognitoId,
            nurseName: selectedNurse.nurse_given_name,
          }).unwrap();

          if (response.success && response.data?.conversation) {
            const conversation = response.data.conversation;
            const newConversationId =
              conversation.id || (conversation as { _id?: string })._id;
            setConversationId(newConversationId);
          } else {
            throw new Error(
              response.message || 'Failed to create conversation'
            );
          }
        } catch (error) {
          console.error('Error creating conversation:', error);
          toast({
            title: 'Chat Error',
            description:
              error instanceof Error
                ? error.message
                : 'Failed to create conversation',
            variant: 'destructive',
          });
          onClose();
        }
      };

      createConversationForNurse();
    }
  }, [
    selectedNurse,
    isOpen,
    conversationId,
    createConversation,
    onClose,
    toast,
  ]);

  // Handle joining conversation when modal opens and WebSocket connects
  useEffect(() => {
    if (conversationId && wsStatus.connected && isOpen) {
      // Leave previous conversation if switching
      if (
        previousConversationIdRef.current &&
        previousConversationIdRef.current !== conversationId
      ) {
        leaveConversation(previousConversationIdRef.current);
      }

      joinConversation(conversationId);
      previousConversationIdRef.current = conversationId;
    }
  }, [
    conversationId,
    wsStatus.connected,
    isOpen,
    joinConversation,
    leaveConversation,
  ]);

  // Handle leaving conversation when modal closes (before WebSocket disconnects)
  useEffect(() => {
    if (!isOpen && previousConversationIdRef.current && wsStatus.connected) {
      leaveConversation(previousConversationIdRef.current);
      previousConversationIdRef.current = null;
    }
  }, [isOpen, wsStatus.connected, leaveConversation]);

  // Cleanup timeout and observer on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const prevMessagesDataRef = useRef<any>(null);

  useEffect(() => {
    if (
      messagesData?.success &&
      (messagesData.data?.messages || messagesData.messages)
    ) {
      const rawMessages =
        messagesData.data?.messages || messagesData.messages || [];

      // Check if messages have actually changed to prevent infinite re-renders
      const messagesChanged =
        !prevMessagesDataRef.current ||
        JSON.stringify(rawMessages) !== JSON.stringify(prevMessagesDataRef.current);

      if (messagesChanged) {
        prevMessagesDataRef.current = rawMessages;

        if (rawMessages.length > 0 && 'id' in rawMessages[0]) {
          setMessages(rawMessages);
        } else {
          const transformedMessages: Message[] = (
            rawMessages as unknown as Record<string, unknown>[]
          ).map(
            (msg): Message => ({
              id: String(msg._id || msg.id || Date.now()),
              conversationId: String(msg.conversationId || conversationId),
              senderId: String(msg.senderId || msg.sender_id || ''),
              senderType: (msg.senderType || msg.sender_type || 'nurse') as
                | 'nurse'
                | 'patient',
              senderName: String(msg.senderName || msg.sender_name || 'Unknown'),
              content: String(msg.content || msg.message || msg.text || ''),
              type: (msg.type || msg.messageType || 'text') as
                | 'text'
                | 'image'
                | 'file',
              status: (msg.status || 'sent') as 'sent' | 'delivered' | 'read',
              timestamp: String(
                msg.timestamp ||
                  msg.createdAt ||
                  msg.created_at ||
                  new Date().toISOString()
              ),
              metadata: (msg.metadata as Record<string, unknown>) || {},
            })
          );
          setMessages(transformedMessages);
        }

        if (conversationId) {
          markAsReadRef.current(conversationId).catch(err =>
            console.error('Error marking messages as read:', err)
          );
        }
      }
    }
  }, [messagesData, conversationId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    const messageElements = document.querySelectorAll('[data-message-id]');
    setupMessageObserver(messageElements);
  }, [messages, setupMessageObserver]);

  useEffect(() => {
    const handleFocus = () => {
      if (conversationId && isOpen) {
        markAsReadRef.current(conversationId).catch(err =>
          console.error('Error marking messages as read:', err)
        );
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden && conversationId && isOpen) {
        markAsReadRef.current(conversationId).catch(err =>
          console.error('Error marking messages as read:', err)
        );
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [conversationId, isOpen]);

  const handleTyping = useCallback(() => {
    if (!isTyping && conversationId) {
      setIsTyping(true);
      sendTypingIndicator(conversationId);
    }

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 3000);

    setTypingTimeout(timeout);
  }, [isTyping, conversationId, sendTypingIndicator, typingTimeout]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !conversationId || isSendingMessage) return;

    try {
      const result = await sendMessage({
        conversationId,
        content: newMessage.trim(),
        type: 'text',
      }).unwrap();

      if (result.success) {
        const sentMessage = result.data?.message || {
          id: String(Date.now()),
          conversationId,
          senderId: userId,
          senderType: 'patient',
          senderName: userGivenName,
          content: newMessage.trim(),
          type: 'text',
          status: 'sent',
          timestamp: new Date().toISOString(),
        };
        setMessages(prev => [...prev, sentMessage]);

        sendTextMessage(conversationId, newMessage.trim());
        setNewMessage('');
        setIsTyping(false);
        if (typingTimeout) {
          clearTimeout(typingTimeout);
        }

        refetchMessages();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to send message',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    handleTyping();
  };

  const handleClose = () => {
    setConversationId(null);
    setMessages([]);
    setNewMessage('');
    setIsTyping(false);
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    onClose();
  };

  const isLoading =
    isCreatingConversation ||
    isLoadingConversation ||
    (isLoadingMessages && !messages.length);

  const displayMessages = messages;

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg shadow-xl w-full max-w-md h-[600px] flex flex-col'>
        {}
        <div className='flex items-center justify-between p-4 border-b'>
          <div className='flex items-center'>
            <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center mr-3'>
              <span className='text-white text-sm font-semibold'>
                {selectedNurse?.nurse_given_name.charAt(0).toUpperCase() || 'N'}
              </span>
            </div>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                {selectedNurse?.nurse_given_name || 'Nurse'}
              </h3>
              <p className='text-sm text-gray-500'>Nurse</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className='p-2 hover:bg-gray-100 rounded-full'
            aria-label='Close chat'
          >
            <X className='h-5 w-5 text-gray-600' />
          </button>
        </div>

        {}
        <div className='flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50'>
          {isLoading ? (
            <div className='flex items-center justify-center h-full'>
              <div className='text-center'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-blue mx-auto'></div>
                <p className='mt-2 text-sm text-gray-600'>
                  Loading conversation...
                </p>
              </div>
            </div>
          ) : displayMessages.length === 0 ? (
            <div className='text-center text-gray-500 py-8'>
              <MessageSquare className='h-8 w-8 mx-auto mb-2 opacity-50' />
              <p className='text-sm'>
                No messages yet. Start the conversation!
              </p>
              {messagesData && !messagesData.success && (
                <p className='text-red-500 text-sm mt-2'>
                  API Error: {messagesData.message}
                </p>
              )}
            </div>
          ) : (
            <>
              {displayMessages.map((message, idx) => (
                <div
                  key={
                    message.id
                      ? `${message.id}-${message.timestamp}`
                      : `msg-${idx}`
                  }
                  data-message-id={message.id || `msg-${idx}`}
                  data-sender-id={message.senderId}
                  className={`flex ${message.senderId === userId ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs px-3 py-2 rounded-lg ${
                      message.senderId === userId
                        ? 'bg-nursery-blue text-white'
                        : 'bg-white border border-gray-200 text-gray-800'
                    }`}
                  >
                    <p className='text-sm'>{message.content}</p>
                    <p
                      className={`text-xs mt-1 ${message.senderId === userId ? 'text-blue-100' : 'text-gray-500'}`}
                      title={safeFormatDate(
                        message.timestamp,
                        'PPpp',
                        'Invalid date'
                      )}
                    >
                      {safeFormatDate(message.timestamp, 'h:mm a', '--')}
                      {message.status === 'read' &&
                        message.senderId === userId && (
                          <span className='ml-1'>✓</span>
                        )}
                    </p>
                  </div>
                </div>
              ))}

              {}
              {Object.values(typingUsers)
                .filter(user => user.userId !== userId)
                .map(user => (
                  <div key={user.userId} className='flex justify-start'>
                    <div className='bg-white border border-gray-200 text-gray-800 px-3 py-2 rounded-lg'>
                      <div className='flex space-x-1'>
                        <div
                          className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                          style={{ animationDelay: '0ms' }}
                        ></div>
                        <div
                          className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                          style={{ animationDelay: '300ms' }}
                        ></div>
                        <div
                          className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                          style={{ animationDelay: '600ms' }}
                        ></div>
                      </div>
                      <p className='text-xs mt-1 text-gray-500'>
                        {user.userName} is typing...
                      </p>
                    </div>
                  </div>
                ))}
            </>
          )}

          <div ref={messagesEndRef} />
        </div>

        {}
        <div className='border-t p-4 bg-white'>
          <div className='flex space-x-2'>
            <Input
              value={newMessage}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
              placeholder='Type your message...'
              disabled={isSendingMessage || !wsStatus.connected}
              className='flex-1'
              aria-label='Message input'
            />
            <Button
              onClick={handleSendMessage}
              disabled={
                !newMessage.trim() || isSendingMessage || !wsStatus.connected
              }
              className='bg-nursery-blue hover:bg-nursery-blue/90'
              aria-label='Send message'
            >
              {isSendingMessage ? (
                <Loader2 className='h-4 w-4 animate-spin' />
              ) : (
                <Send className='h-4 w-4' />
              )}
            </Button>
          </div>

          {}
          {!wsStatus.connected && wsStatus.error && (
            <p className='text-xs text-amber-600 mt-2 flex items-center'>
              <AlertCircle className='h-3 w-3 mr-1' />
              Connection issue. Messages may be delayed.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatModal;
